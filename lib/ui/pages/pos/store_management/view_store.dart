import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/take_product_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class ViewStore extends StatefulWidget {
  const ViewStore({super.key});

  @override
  State<ViewStore> createState() => _ViewStoreState();
}

class _ViewStoreState extends State<ViewStore> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        title: "View Specific Store",
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 16.w),
        children: [
          const DiscountOverviewCard(),
          SizedBox(
            height: 24.h,
          ),
          const DashBoardHeader(
            title: "Store Details",
            subtitle: "A basic overview of Sub-category details",
            padding: EdgeInsets.zero,
            showCTA: false,
          ),
          SizedBox(
            height: 16.h,
          ),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: ColorPath.athensGrey)),
            child: Column(
              children: [
                PaymentBreakdownItem(
                  title: "Created by:",
                  child: Text(
                    "Dairo Isaac",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Date Created:",
                  child: Text(
                    "April 11, 2025 | 10:00 am",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Number of Staffs:",
                  child: Text(
                    "May 11, 2025",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "GLA:",
                  child: Text(
                    "784sqm",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "GSA:",
                  child: Text(
                    "784sqm",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Country:",
                  child: Text(
                    "Nigeria",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "State:",
                  child: Text(
                    "Lagos State",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Local Government Area:",
                  child: Text(
                    "Kosofe",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Address:",
                  child: Text(
                    "5, Ireku Street, Oworo",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Status:",
                  child: ColorTag(
                    color:
                        1 + 1 == 3 ? ColorPath.earlyDawn : ColorPath.foamGreen,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.circle,
                          size: 6,
                          color: 1 + 1 == 3
                              ? ColorPath.californiaOrange
                              : ColorPath.meadowGreen,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          "Active",
                          style: textTheme.bodySmall?.copyWith(
                              color: 1 + 1 == 3
                                  ? ColorPath.vesuvius
                                  : ColorPath.funGreen),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Number of Customer:",
                  child: Text(
                    "10",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Total Store Revenue:",
                  child: NairaDisplay(
                    amount: 1903484,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.text4,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 24.h,
          ),
          DashBoardHeader(
            title: "Store Recent Order",
            subtitle: "Manage your Store rder with ease",
            onPressed: () {},
            child: Row(
              children: [
                Text(
                  "View All",
                  style: textTheme.bodySmall,
                ),
                Icon(
                  Icons.north_east,
                  size: 14.w,
                  color: ColorPath.flamingo,
                )
              ],
            ),
          ),
          1 + 1 == 3
              ? Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    subTitle: "You currently don’t have any Order added Yet",
                    buttonText: "Add New Customer",
                    onPressed: () {},
                  ),
                )
              : ListView.separated(
                  padding: EdgeInsets.only(top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(color: ColorPath.athensGrey)),
                      child: Row(
                        children: [
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "#6784",
                                style: textTheme.bodySmall
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              Text.rich(
                                TextSpan(text: "Number of Item: ", children: [
                                  TextSpan(
                                      text: "20",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: colorScheme.text4,
                                          fontWeight: FontWeight.bold))
                                ]),
                                style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.subTextSecondary),
                              )
                            ],
                          )),
                          SizedBox(
                            width: 12.w,
                          ),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              NairaDisplay(
                                amount: double.parse("200789.90"),
                                fontSize: 16.sp,
                                color: colorScheme.text4,
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              ColorTag(
                                color: 1 + 1 == 2
                                    ? ColorPath.earlyDawn
                                    : ColorPath.foamGreen,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.circle,
                                      size: 6,
                                      color: 1 + 1 == 2
                                          ? ColorPath.californiaOrange
                                          : ColorPath.meadowGreen,
                                    ),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Text(
                                      "Pending",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: 1 + 1 == 2
                                              ? ColorPath.vesuvius
                                              : ColorPath.funGreen),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          )),
                        ],
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3),
          SizedBox(
            height: 168.h,
          )
        ],
      ),
      bottomSheet: 1 + 1 == 2 // if Take Action
          ? Container(
              height: 163.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                    color: ColorPath.mischkaGrey,
                    blurRadius: 4.r,
                    spreadRadius: 4.r,
                    blurStyle: BlurStyle.outer)
              ]),
              child: Column(
                children: [
                  Text(
                    "Edit, Delete or Deactivate a Simple Product",
                    style:
                        textTheme.bodySmall?.copyWith(color: colorScheme.text7),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  CustomButton(
                    onPressed: () {
                      bottomSheetWrapper(
                          context: context,
                          child: TakeProductActionBottomsheet(
                            isVariation: false,
                            variationId: "1234",
                          ));
                    },
                    borderColor: ColorPath.flamingo,
                    bgColor: Colors.white,
                    buttonTextColor: ColorPath.flamingo,
                    buttonText: "Take Action",
                  )
                ],
              ),
            )
          : const SizedBox(),
    );
  }
}

// WIDGETS
class DiscountOverviewCard extends StatelessWidget {
  const DiscountOverviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: ColorPath.flamingo.withOpacity(.08),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.flamingoRed)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "TOTAL STORE REVENUE",
            style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600, color: colorScheme.text7),
          ),
          SizedBox(
            height: 8.h,
          ),
          NairaDisplay(
            amount: 280390,
            color: colorScheme.text6,
          ),
          SizedBox(
            height: 8.h,
          ),
          Row(
            children: [
              ColorTag(
                color: ColorPath.emeraldGreen,
                child: Row(
                  children: [
                    Text(
                      '4.9%',
                      style: textTheme.bodySmall?.copyWith(color: Colors.white),
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    const Icon(
                      Icons.north_east,
                      size: 12,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                'last 3 days',
                style: textTheme.bodySmall,
              )
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'Total Order',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.mirageBlack,
                  subTitle: "200",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                  title: 'Completed Order',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.salemGreen,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  subTitle: "100",
                ),
              ),
            ],
          ),
          SizedBox(
            height: 12.h,
          ),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'Processing Order ',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.mirageBlack,
                  subTitle: "2",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                    title: 'Cancelled Order',
                    titleSize: 14.sp,
                    titleFontWeight: FontWeight.w400,
                    subtitleFontWeight: FontWeight.w600,
                    subtitleColor: ColorPath.alizarinRed,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    subTitle: "20"),
              ),
            ],
          )
        ],
      ),
    );
  }
}

class PaymentBreakdownItem extends StatelessWidget {
  final String title;
  final Widget? child;
  const PaymentBreakdownItem({super.key, this.title = '', this.child});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
        )),
        Row(
          children: [
            child ?? Container(),
          ],
        )
      ],
    );
  }
}
