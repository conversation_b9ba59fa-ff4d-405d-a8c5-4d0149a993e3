import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/quick_action_item.dart';
import 'package:quick_retail_mobile/core/data/services/navigation_service.dart';
import 'package:quick_retail_mobile/core/data/view_models/product_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';

class TakeProductActionBottomsheet extends ConsumerStatefulWidget {
  const TakeProductActionBottomsheet({
    super.key,
    this.isVariation = false,
    required this.variationId,
  });

  final bool isVariation;
  final String? variationId;

  @override
  ConsumerState<TakeProductActionBottomsheet> createState() =>
      _TakeProductActionBottomsheetState();
}

class _TakeProductActionBottomsheetState
    extends ConsumerState<TakeProductActionBottomsheet> {
  late final List<QuickActionsItem> quickActions;

  @override
  void initState() {
    super.initState();
    quickActions = [
      QuickActionsItem(
        actionName: "Edit Product",
        subtitle: "Save and update new changes ",
        assetName: "edit",
        onPressed: () {
          _pop();
          bottomSheetWrapper(
              context:
                  locator<NavigationService>().navigationKey.currentContext!,
              child: CustomBottomSheet(
                title: "Save and Update New Changes ?",
                subTitle:
                    "Are you sure you want to Save and update new changes on this Simple product ? Kindly note that this new changes would override the old changes. ",
                firstButtonText: "Yes, Save and Update New Changes",
                secondButtonText: "No, Close",
                onPressedFirst: () {},
                onPressedSecond: () {
                  popNavigation(
                    context: locator<NavigationService>()
                        .navigationKey
                        .currentContext!,
                  );
                },
              ));
        },
      ),
      QuickActionsItem(
        actionName: "Deactivate Product",
        subtitle: "Temporarily deactivate product for sale",
        assetName: "toggle",
        onPressed: () {
          bottomSheetWrapper(
              context:
                  locator<NavigationService>().navigationKey.currentContext!,
              child: CustomBottomSheet(
                title: "Save and Update New Changes ?",
                subTitle:
                    "Are you sure you want to Save and update new changes on this Simple product ? Kindly note that this new changes would override the old changes. ",
                firstButtonText: "Yes, Save and Update New Changes",
                secondButtonText: "No, Close",
                onPressedFirst: () {},
                onPressedSecond: () {
                  popNavigation(
                    context: locator<NavigationService>()
                        .navigationKey
                        .currentContext!,
                  );
                },
              ));
        },
      ),
      QuickActionsItem(
        actionName: "Reactivate Product",
        subtitle: "Reactivate an already deactivated product",
        assetName: "toggle",
        onPressed: () {
          bottomSheetWrapper(
              context:
                  locator<NavigationService>().navigationKey.currentContext!,
              child: CustomBottomSheet(
                title: "Save and Update New Changes ?",
                subTitle:
                    "Are you sure you want to Save and update new changes on this Simple product ? Kindly note that this new changes would override the old changes. ",
                firstButtonText: "Yes, Save and Update New Changes",
                secondButtonText: "No, Close",
                onPressedFirst: () {},
                onPressedSecond: () {
                  popNavigation(
                    context: locator<NavigationService>()
                        .navigationKey
                        .currentContext!,
                  );
                },
              ));
        },
      ),
      QuickActionsItem(
        actionName: "Delete Product",
        subtitle: "Remove product and it details permanently",
        assetName: "trashFlamingo",
        onPressed: () {
          bottomSheetWrapper(
              context:
                  locator<NavigationService>().navigationKey.currentContext!,
              child: CustomBottomSheet(
                title: "Delete Simple Product ?",
                subTitle:
                    "Are you sure you want to delete this Simple product? Kindly note that this action is irreversible and permanent, therefore product data would be wiped out from the store database. ",
                firstButtonText: "Yes, Delete Simple product",
                secondButtonText: "No, Close",
                onPressedFirst: () async {
                  _pop();
                  await ref
                      .read(productViewModel.notifier)
                      .deleteProduct(widget.variationId!);

                  if (ref.read(productViewModel).state == ViewState.retrieved) {
                    bottomSheetWrapper(
                        context: locator<NavigationService>()
                            .navigationKey
                            .currentContext!,
                        child: CustomBottomSheet(
                          title:
                              '${widget.isVariation ? 'Variation' : 'Simple'} Product Deleted',
                          subTitle:
                              'Congratulations, you have successfully deleted this ${widget.isVariation ? 'Variation' : 'Simple'} product',
                          firstButtonText: "Manage all Product",
                          secondButtonText: "Create New Product",
                          onPressedFirst: () {
                            _pop();
                            _pop();
                            _pop();
                            _fetchProducts();
                          },
                          onPressedSecond: () {
                            popNavigation(context: context);
                          },
                        ));
                  } else {
                    showFlushBar(
                        context: context,
                        message: ref.read(productViewModel).message,
                        success: false);
                  }
                },
                onPressedSecond: () {
                  _pop();
                },
              ));
        },
      ),
    ];
  }

  _fetchProducts() {
    ref.read(productViewModel).fetchAllProducts();
  }

  _pop() {
    popNavigation(
      context: locator<NavigationService>().navigationKey.currentContext!,
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Take action",
          subTitle: "Take action on a specific product today",
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return Clickable(
                onPressed: quickActions[index].onPressed,
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorPath.athensGrey),
                      borderRadius: BorderRadius.circular(8.r)),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorPath.flamingo.withOpacity(.2)),
                        child: SvgPicture.asset(
                            Utilities.getSvg(quickActions[index].assetName)),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(quickActions[index].actionName),
                            Text(
                              quickActions[index].subtitle ?? '',
                              style: textTheme.bodySmall
                                  ?.copyWith(color: colorScheme.subTextPrimary),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: quickActions.length),
        SizedBox(
          height: 24.h,
        ),
        CustomButton(
          onPressed: () {
            popNavigation(context: context);
          },
          borderColor: ColorPath.flamingo,
          bgColor: Colors.white,
          buttonTextColor: ColorPath.flamingo,
          buttonText: "No, Close",
        )
      ],
    );
  }
}
