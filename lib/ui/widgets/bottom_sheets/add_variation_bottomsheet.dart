import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/models/create_product_arg.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

import 'bottomsheet_header.dart';

class AddVariationBottomsheet extends ConsumerStatefulWidget {
  const AddVariationBottomsheet({
    super.key,
    this.editingValue,
    required this.returningValue,
  });

  final VariationArg? editingValue;
  final ValueChanged<VariationArg> returningValue;

  @override
  ConsumerState<AddVariationBottomsheet> createState() =>
      _AddVariationBottomsheetState();
}

class _AddVariationBottomsheetState
    extends ConsumerState<AddVariationBottomsheet> {
  final formKey = GlobalKey<FormState>();

  final sizeC = TextEditingController();
  final colorC = TextEditingController();
  final costPriceC = TextEditingController();
  final sellingPriceC = TextEditingController();
  final availableQuantityC = TextEditingController();
  final reorderLevelC = TextEditingController();

  final sizeF = FocusNode();
  final colorF = FocusNode();
  final costPriceF = FocusNode();
  final sellingPriceF = FocusNode();
  final availableQuantityF = FocusNode();
  final reorderLevelF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setEditingValue();
    });
  }

  _setEditingValue() {
    if (widget.editingValue != null) {
      sizeC.text = widget.editingValue!.size ?? '';
      colorC.text = widget.editingValue!.colour ?? '';
      costPriceC.text = widget.editingValue!.costPrice?.toString() ?? '';
      sellingPriceC.text = widget.editingValue!.sellingPrice?.toString() ?? '';
      availableQuantityC.text = widget.editingValue!.quantity?.toString() ?? '';
      reorderLevelC.text = widget.editingValue!.reorderLevel?.toString() ?? '';
    }

    setState(() {});
  }

  @override
  void dispose() {
    sizeC.dispose();
    colorC.dispose();
    costPriceC.dispose();
    sellingPriceC.dispose();
    availableQuantityC.dispose();
    reorderLevelC.dispose();

    sizeF.dispose();
    colorF.dispose();
    costPriceF.dispose();
    sellingPriceF.dispose();
    availableQuantityF.dispose();
    reorderLevelF.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: SizedBox(
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const BottomSheetHeader(
              title: "Add a Variation",
              subTitle: "Add a variation type and option to your product",
            ),
            SizedBox(height: 24.h),
            Container(height: 1, color: ColorPath.athensGrey),
            SizedBox(
              height: 16.h,
            ),
            Expanded(
              child: Form(
                key: formKey,
                child: ListView(
                  children: [
                    CustomTextField(
                      controller: sizeC,
                      focusPointer: sizeF,
                      label: "Size",
                      // bottomHintText: "Option such as size, color etc",
                      isCompulsory: false,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "Please enter a size";
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.h),
                    CustomTextField(
                      controller: colorC,
                      focusPointer: colorF,
                      label: "Colour",
                      // bottomHintText: "Separate with comma “,” to add multiple. ",
                      isCompulsory: false,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "Please enter a color";
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.h),
                    CustomTextField(
                      controller: costPriceC,
                      focusPointer: costPriceF,
                      label: "Cost Price",
                      isCompulsory: false,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "Please enter a cost price";
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.h),
                    CustomTextField(
                      controller: sellingPriceC,
                      focusPointer: sellingPriceF,
                      label: "Selling Price",
                      isCompulsory: false,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "Please enter a selling price";
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.h),
                    CustomTextField(
                      controller: availableQuantityC,
                      focusPointer: availableQuantityF,
                      label: "Available Quantity",
                      isCompulsory: false,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "Please enter a available quantity";
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.h),
                    CustomTextField(
                      controller: reorderLevelC,
                      focusPointer: reorderLevelF,
                      label: "Reorder Level",
                      isCompulsory: false,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "Please enter a reorder level";
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 64.h),
                    CustomButton(
                      onPressed: () {
                        if (!formKey.currentState!.validate()) return;

                        widget.returningValue(VariationArg(
                          size: sizeC.text,
                          colour: colorC.text,
                          costPrice: int.tryParse(costPriceC.text),
                          sellingPrice: int.tryParse(sellingPriceC.text),
                          quantity: int.tryParse(availableQuantityC.text),
                          reorderLevel: int.tryParse(reorderLevelC.text),
                        ));
                        popNavigation(context: context);
                      },
                      buttonText: "Add Variation Option",
                    ),
                    SizedBox(height: 16.h),
                    CustomButton(
                      onPressed: () {
                        popNavigation(context: context);
                      },
                      buttonText: "No, Close",
                      buttonTextColor: ColorPath.flamingo,
                      borderColor: ColorPath.flamingo,
                      bgColor: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
