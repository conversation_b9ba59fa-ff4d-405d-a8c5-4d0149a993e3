import 'package:quick_retail_mobile/core/data/models/category_response.dart';

class ProductResponse {
  final bool? error;
  final String? message;
  final Data? data;

  ProductResponse({
    this.error,
    this.message,
    this.data,
  });

  factory ProductResponse.fromJson(Map<String, dynamic> json) =>
      ProductResponse(
        error: json["error"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  final Products? products;
  final int? totalItems;
  final String? totalRevenue;
  final int? active;
  final int? inactive;

  Data({
    this.products,
    this.totalItems,
    this.totalRevenue,
    this.active,
    this.inactive,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        products: json["products"] == null
            ? null
            : Products.fromJson(json["products"]),
        totalItems: json["totalItems"],
        totalRevenue: json["total_revenue"],
        active: json["active"],
        inactive: json["inactive"],
      );

  Map<String, dynamic> toJson() => {
        "products": products?.toJson(),
        "totalItems": totalItems,
        "total_revenue": totalRevenue,
        "active": active,
        "inactive": inactive,
      };
}

class Products {
  final int? currentPage;
  final List<ProductData>? data;
  final String? firstPageUrl;
  final int? from;
  final int? lastPage;
  final String? lastPageUrl;
  final List<Link>? links;
  final String? nextPageUrl;
  final String? path;
  final int? perPage;
  final dynamic prevPageUrl;
  final int? to;
  final int? total;

  Products({
    this.currentPage,
    this.data,
    this.firstPageUrl,
    this.from,
    this.lastPage,
    this.lastPageUrl,
    this.links,
    this.nextPageUrl,
    this.path,
    this.perPage,
    this.prevPageUrl,
    this.to,
    this.total,
  });

  factory Products.fromJson(Map<String, dynamic> json) => Products(
        currentPage: json["current_page"],
        data: json["data"] == null
            ? []
            : List<ProductData>.from(
                json["data"]!.map((x) => ProductData.fromJson(x))),
        firstPageUrl: json["first_page_url"],
        from: json["from"],
        lastPage: json["last_page"],
        lastPageUrl: json["last_page_url"],
        links: json["links"] == null
            ? []
            : List<Link>.from(json["links"]!.map((x) => Link.fromJson(x))),
        nextPageUrl: json["next_page_url"],
        path: json["path"],
        perPage: json["per_page"],
        prevPageUrl: json["prev_page_url"],
        to: json["to"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "first_page_url": firstPageUrl,
        "from": from,
        "last_page": lastPage,
        "last_page_url": lastPageUrl,
        "links": links == null
            ? []
            : List<dynamic>.from(links!.map((x) => x.toJson())),
        "next_page_url": nextPageUrl,
        "path": path,
        "per_page": perPage,
        "prev_page_url": prevPageUrl,
        "to": to,
        "total": total,
      };
}

class ProductData {
  final int? id;
  final String? variationId;
  final String? name;
  final String? sku;
  final String? ean;
  final String? code;
  final String? costPrice;
  final String? sellingPrice;
  final int? quantity;
  final String? reorderLevel;
  final String? imagePath;
  final dynamic reason;
  final int? quantitySupplied;
  final int? quantityAvailable;
  final int? quantitySold;
  final int? quantityAvailableInStores;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? stockStatus;
  final Product? product;
  final List<Discount>? discounts;
  final List<VariationAttribute>? variationAttributes;
  final List<ProductInventory>? productInventory;
  final Category? category;
  final Category? subCategory;

  ProductData({
    this.id,
    this.variationId,
    this.name,
    this.sku,
    this.ean,
    this.code,
    this.costPrice,
    this.sellingPrice,
    this.quantity,
    this.reorderLevel,
    this.imagePath,
    this.reason,
    this.quantitySupplied,
    this.quantityAvailable,
    this.quantitySold,
    this.quantityAvailableInStores,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.stockStatus,
    this.product,
    this.discounts,
    this.variationAttributes,
    this.productInventory,
    this.category,
    this.subCategory,
  });

  factory ProductData.fromJson(Map<String, dynamic> json) => ProductData(
        id: json["id"],
        variationId: json["variationID"],
        name: json["name"],
        sku: json["sku"],
        ean: json["ean"],
        code: json["code"],
        costPrice: json["cost_price"],
        sellingPrice: json["selling_price"],
        quantity: json["quantity"],
        reorderLevel: json["reorder_level"],
        imagePath: json["image_path"],
        reason: json["reason"],
        quantitySupplied: json["quantity_supplied"],
        quantityAvailable: json["quantity_available"],
        quantitySold: json["quantity_sold"],
        quantityAvailableInStores: json["quantity_available_in_stores"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        stockStatus: json["stock_status"],
        product:
            json["product"] == null ? null : Product.fromJson(json["product"]),
        discounts: json["discounts"] == null
            ? []
            : List<Discount>.from(
                json["discounts"]!.map((x) => Discount.fromJson(x))),
        variationAttributes: json["variation_attributes"] == null
            ? []
            : List<VariationAttribute>.from(json["variation_attributes"]!
                .map((x) => VariationAttribute.fromJson(x))),
        productInventory: json["product_inventory"] == null
            ? []
            : List<ProductInventory>.from(json["product_inventory"]!
                .map((x) => ProductInventory.fromJson(x))),
        category: json["category"] == null
            ? null
            : Category.fromJson(json["category"]),
        subCategory: json["sub_category"] == null
            ? null
            : Category.fromJson(json["sub_category"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "variationID": variationId,
        "name": name,
        "sku": sku,
        "ean": ean,
        "code": code,
        "cost_price": costPrice,
        "selling_price": sellingPrice,
        "quantity": quantity,
        "reorder_level": reorderLevel,
        "image_path": imagePath,
        "reason": reason,
        "quantity_supplied": quantitySupplied,
        "quantity_available": quantityAvailable,
        "quantity_sold": quantitySold,
        "quantity_available_in_stores": quantityAvailableInStores,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "stock_status": stockStatus,
        "product": product?.toJson(),
        "discounts": discounts == null
            ? []
            : List<dynamic>.from(discounts!.map((x) => x.toJson())),
        "variation_attributes": variationAttributes == null
            ? []
            : List<dynamic>.from(variationAttributes!.map((x) => x.toJson())),
        "product_inventory": productInventory == null
            ? []
            : List<dynamic>.from(productInventory!.map((x) => x.toJson())),
        "category": category?.toJson(),
        "sub_category": subCategory?.toJson(),
      };
}

class Discount {
  final int? productVariationId;
  final String? code;
  final String? name;
  final DateTime? from;
  final DateTime? to;
  final String? type;
  final int? laravelThroughKey;
  final String? status;

  Discount({
    this.productVariationId,
    this.code,
    this.name,
    this.from,
    this.to,
    this.type,
    this.laravelThroughKey,
    this.status,
  });

  factory Discount.fromJson(Map<String, dynamic> json) => Discount(
        productVariationId: json["product_variation_id"],
        code: json["code"],
        name: json["name"],
        from: json["from"] == null ? null : DateTime.parse(json["from"]),
        to: json["to"] == null ? null : DateTime.parse(json["to"]),
        type: json["type"],
        laravelThroughKey: json["laravel_through_key"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "product_variation_id": productVariationId,
        "code": code,
        "name": name,
        "from": from?.toIso8601String(),
        "to": to?.toIso8601String(),
        "type": type,
        "laravel_through_key": laravelThroughKey,
        "status": status,
      };
}

class Product {
  final String? productId;
  final int? categoryId;
  final int? subCategoryId;
  final int? locationId;
  final dynamic vendorId;
  final String? productName;
  final String? productCode;
  final String? sku;
  final String? shortDescription;
  final String? longDescription;
  final String? featuredImage;
  final String? imagePath;
  final dynamic ean;
  final dynamic taxId;
  final String? costPrice;
  final String? sellingPrice;
  final dynamic discountPercentage;
  final dynamic promotionalPrice;
  final dynamic promotionalStartDate;
  final dynamic promotionalEndDate;
  final int? totalQuantity;
  final dynamic safetyInstructions;
  final dynamic complianceCertificates;
  final String? tags;
  final dynamic notes;
  final dynamic reason;
  final String? status;
  final int? hasVariations;
  final int? isDraft;
  final int? createdBy;
  final dynamic deletedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Location? location;
  final Category? category;
  final Category? subCategory;

  Product({
    this.productId,
    this.categoryId,
    this.subCategoryId,
    this.locationId,
    this.vendorId,
    this.productName,
    this.productCode,
    this.sku,
    this.shortDescription,
    this.longDescription,
    this.featuredImage,
    this.imagePath,
    this.ean,
    this.taxId,
    this.costPrice,
    this.sellingPrice,
    this.discountPercentage,
    this.promotionalPrice,
    this.promotionalStartDate,
    this.promotionalEndDate,
    this.totalQuantity,
    this.safetyInstructions,
    this.complianceCertificates,
    this.tags,
    this.notes,
    this.reason,
    this.status,
    this.hasVariations,
    this.isDraft,
    this.createdBy,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.location,
    this.category,
    this.subCategory,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        productId: json["productID"],
        categoryId: json["category_id"],
        subCategoryId: json["sub_category_id"],
        locationId: json["location_id"],
        vendorId: json["vendor_id"],
        productName: json["product_name"],
        productCode: json["product_code"],
        sku: json["sku"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        featuredImage: json["featured_image"],
        imagePath: json["image_path"],
        ean: json["ean"],
        taxId: json["tax_id"],
        costPrice: json["cost_price"],
        sellingPrice: json["selling_price"],
        discountPercentage: json["discount_percentage"],
        promotionalPrice: json["promotional_price"],
        promotionalStartDate: json["promotional_start_date"],
        promotionalEndDate: json["promotional_end_date"],
        totalQuantity: json["total_quantity"],
        safetyInstructions: json["safety_instructions"],
        complianceCertificates: json["compliance_certificates"],
        tags: json["tags"],
        notes: json["notes"],
        reason: json["reason"],
        status: json["status"],
        hasVariations: json["has_variations"],
        isDraft: json["is_draft"],
        createdBy: json["created_by"],
        deletedAt: json["deleted_at"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        location: json["location"] == null
            ? null
            : Location.fromJson(json["location"]),
        category: json["category"] == null
            ? null
            : Category.fromJson(json["category"]),
        subCategory: json["sub_category"] == null
            ? null
            : Category.fromJson(json["sub_category"]),
      );

  Map<String, dynamic> toJson() => {
        "productID": productId,
        "category_id": categoryId,
        "sub_category_id": subCategoryId,
        "location_id": locationId,
        "vendor_id": vendorId,
        "product_name": productName,
        "product_code": productCode,
        "sku": sku,
        "short_description": shortDescription,
        "long_description": longDescription,
        "featured_image": featuredImage,
        "image_path": imagePath,
        "ean": ean,
        "tax_id": taxId,
        "cost_price": costPrice,
        "selling_price": sellingPrice,
        "discount_percentage": discountPercentage,
        "promotional_price": promotionalPrice,
        "promotional_start_date": promotionalStartDate,
        "promotional_end_date": promotionalEndDate,
        "total_quantity": totalQuantity,
        "safety_instructions": safetyInstructions,
        "compliance_certificates": complianceCertificates,
        "tags": tags,
        "notes": notes,
        "reason": reason,
        "status": status,
        "has_variations": hasVariations,
        "is_draft": isDraft,
        "created_by": createdBy,
        "deleted_at": deletedAt,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "location": location?.toJson(),
        "category": category?.toJson(),
        "sub_category": subCategory?.toJson(),
      };
}

class Location {
  final int? id;
  final String? name;
  final String? locationId;

  Location({
    this.id,
    this.name,
    this.locationId,
  });

  factory Location.fromJson(Map<String, dynamic> json) => Location(
        id: json["id"],
        name: json["name"],
        locationId: json["locationID"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "locationID": locationId,
      };
}

class VariationAttribute {
  final int? productVariationId;
  final String? optionType;
  final String? optionValue;

  VariationAttribute({
    this.productVariationId,
    this.optionType,
    this.optionValue,
  });

  factory VariationAttribute.fromJson(Map<String, dynamic> json) =>
      VariationAttribute(
        productVariationId: json["product_variation_id"],
        optionType: json["option_type"],
        optionValue: json["option_value"],
      );

  Map<String, dynamic> toJson() => {
        "product_variation_id": productVariationId,
        "option_type": optionType,
        "option_value": optionValue,
      };
}

class ProductInventory {
  final int? id;
  final int? productId;
  final dynamic productVariationId;
  final int? locationId;
  final int? quantity;
  final int? reorderLevel;
  final int? totalQuantity;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ProductInventory({
    this.id,
    this.productId,
    this.productVariationId,
    this.locationId,
    this.quantity,
    this.reorderLevel,
    this.totalQuantity,
    this.createdAt,
    this.updatedAt,
  });

  factory ProductInventory.fromJson(Map<String, dynamic> json) =>
      ProductInventory(
        id: json["id"],
        productId: json["product_id"],
        productVariationId: json["product_variation_id"],
        locationId: json["location_id"],
        quantity: json["quantity"],
        reorderLevel: json["reorder_level"],
        totalQuantity: json["total_quantity"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "product_id": productId,
        "product_variation_id": productVariationId,
        "location_id": locationId,
        "quantity": quantity,
        "reorder_level": reorderLevel,
        "total_quantity": totalQuantity,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class Link {
  final String? url;
  final String? label;
  final bool? active;

  Link({
    this.url,
    this.label,
    this.active,
  });

  factory Link.fromJson(Map<String, dynamic> json) => Link(
        url: json["url"],
        label: json["label"],
        active: json["active"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "label": label,
        "active": active,
      };
}

class ProductParams {
  final String? search;
  final String? sortBy;
  final String? locationName;
  final String? categoryName;
  final String? startDate;
  final String? endDate;
  final String? priceFrom;
  final String? priceTo;
  final String? discountStatus;
  final bool? paginate;
  final String? perPage;
  final String? orderStatus;
  final int? stockLevelFrom;
  final int? stockLevelTo;

  ProductParams({
    this.search,
    this.sortBy,
    this.locationName,
    this.categoryName,
    this.startDate,
    this.endDate,
    this.priceFrom,
    this.priceTo,
    this.discountStatus,
    this.paginate = true,
    this.perPage,
    this.orderStatus,
    this.stockLevelFrom,
    this.stockLevelTo,
  });

  Map<String, dynamic> toJson() => {
        "search": search,
        "sort_by": sortBy,
        "location_name": locationName,
        "category_name": categoryName,
        "start_date": startDate,
        "end_date": endDate,
        "price_from": priceFrom,
        "price_to": priceTo,
        "discount_status": discountStatus,
        "paginate": paginate,
        "per_page": perPage,
        "order_status": orderStatus,
        "stock_level_from": stockLevelFrom,
        "stock_level_to": stockLevelTo,
      };
}
