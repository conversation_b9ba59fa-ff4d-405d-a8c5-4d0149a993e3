import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/product_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class ProductViewModel extends BaseState {
  final _productProvider = locator<ProductProvider>();

  //message
  String _message = '';
  String get message => _message;

  createProduct([Map<String, dynamic>? details]) async {
    log("createProduct details $details");
    setState(ViewState.busy);
    details?.removeWhere((k, v) => v == null || v == '');
    await _productProvider.createProduct(details).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  ProductResponse? _productResponse;
  ProductResponse? get productResponse => _productResponse;
  List<ProductData> get products =>
      _productResponse?.data?.products?.data ?? [];

  fetchAllProducts({
    ProductParams? productParams,
    bool showBusyState = true,
  }) async {
    productParams ??= ProductParams();
    final body = productParams.toJson();
    body.removeWhere((k, v) => v == null || v == '');
    if (showBusyState) setState(ViewState.busy);
    await _productProvider.fetchAllProducts(body).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _productResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  ProductData? _singleProduct;
  ProductData? get singleProduct => _singleProduct;

  Future<ProductData?> fetchSingleProduct(String variationId) async {
    setState(ViewState.busy);
    try {
      final response = await _productProvider.fetchSingleProduct(variationId);
      _message = defaultSuccessMessage;
      _singleProduct = response;
      setState(ViewState.retrieved);
      return response;
    } catch (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
      return null;
    }
  }

  deleteProduct(String productId) async {
    setState(ViewState.busy);
    await _productProvider.deleteProduct(productId).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }
}

final productViewModel = ChangeNotifierProvider<ProductViewModel>((ref) {
  return ProductViewModel();
});
